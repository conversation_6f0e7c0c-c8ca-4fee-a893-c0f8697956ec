# Valgrind Memory Issues TODO List

## Critical Memory Management Issues Found

### 1. Object Lifecycle Management

#### 1.1 Corpse Objects Memory Leaks
- [ ] Verify corpse timer expiration properly extracts all contained objects

#### 1.3 Object String Management
- [ ] Audit all places where object strings are duplicated with `strdup()` without freeing existing strings
  - Check for patterns like `obj->name = strdup()` without prior `free(obj->name)`
  - Focus on object creation/modification functions

### 2. Character Memory Management

#### 2.1 Player Special Data
- [ ] Audit account name string management
  - Multiple places duplicate account names without freeing old ones

#### 2.3 Spell/Skill Data
- [ ] Review spell preparation queue cleanup
  - `destroy_spell_prep_queue()` may not free all allocated nodes
- [ ] Check innate magic queue cleanup
- [ ] Verify spell collection and known spells are properly freed

### 3. Script System Memory

#### 3.1 Script Variables
- [ ] Audit DG script variable cleanup
  - Script variables may persist after script extraction
  - Check for leaked variable lists

#### 3.2 Proto Scripts
- [ ] Review `free_proto_script()` usage
  - Ensure prototype scripts aren't freed while instances exist
  - Check reference counting for shared scripts

### 4. Event System Memory

#### 4.1 Event Cleanup
- [ ] Verify all events are cancelled before freeing event lists
  - `free_list()` doesn't cancel events, just frees the list structure
- [ ] Check for events that reference freed objects/characters
  - Events may hold stale pointers after target extraction

### 5. Database/MySQL Related

#### 5.1 Result Set Management
- [ ] Audit all MySQL query results for proper cleanup
  - Many queries don't call `mysql_free_result()`
  - Check for leaked result sets in error paths

#### 5.2 String Escaping
- [ ] Review `mysql_real_escape_string()` usage
  - Escaped strings are often allocated but not freed
  - Check for buffer overflows in escape operations

### 6. Specific Memory Leak Patterns

#### 6.1 Room/Zone Memory
- [ ] Audit room description strings
  - Dynamic room descriptions may leak on zone resets
  - Check trail data cleanup in wilderness areas

#### 6.2 Shop/Trade Memory
- [ ] Review shop inventory management
  - Shop objects may not be properly freed on shutdown
  - Check trade transaction temporary object cleanup

### 7. Performance-Related Memory Issues

#### 7.1 String Duplication
- [ ] Replace excessive `strdup()` calls with reference counting
  - Many identical strings are duplicated unnecessarily
  - Implement string pooling for common strings

#### 7.2 Memory Fragmentation
- [ ] Implement object pooling for frequently allocated structures
  - Objects, characters, and events are constantly allocated/freed
  - Use fixed-size pools to reduce fragmentation

### 8. Testing and Validation

#### 8.1 Valgrind Test Suite
- [ ] Create automated valgrind tests for:
  - Object creation/destruction cycles
  - Character login/logout sequences
  - Combat and death scenarios
  - Container manipulation
  - Script execution

#### 8.2 Memory Tracking
- [ ] Add memory allocation tracking system
  - Track total allocations by type
  - Monitor for gradual memory growth
  - Log allocation/free mismatches

### 9. Code Quality Improvements

#### 9.1 Consistent Memory Management
- [ ] Establish clear ownership rules for all allocated memory
- [ ] Document memory management patterns in code
- [ ] Add assertions to verify memory invariants

#### 9.2 Safe String Handling
- [ ] Create safe string assignment functions
  - Automatically free existing strings
  - Handle NULL cases properly
  - Prevent double-frees

## Priority Order

1. **HIGH**: Audit and fix object string duplications without free
2. **HIGH**: Fix character memory leaks in player special data
3. **MEDIUM**: Implement memory tracking and testing
4. **LOW**: Optimize string management with pooling

## Notes

- Many issues stem from unclear ownership semantics
- Prototype vs instance management needs clear documentation
- Event system needs reference counting or weak references
- Consider implementing RAII patterns where possible in C
